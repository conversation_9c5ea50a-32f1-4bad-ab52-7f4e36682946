{"dependencies": {"@prisma/client": "^6.11.0", "bcrypt": "^6.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "express": "^5.1.0", "jsonwebtoken": "^9.0.2"}, "name": "backend", "version": "1.0.0", "main": "index.js", "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.10", "nodemon": "^3.1.10", "prisma": "^6.11.0", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc --build", "start": "node ./dist/index/js", "start:dev": "nodemon ./src/index.ts"}, "keywords": [], "author": "", "license": "ISC", "description": ""}