"use client"

import React, { useState } from "react"
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog"

export type Product = {
  pID: string;
  pName: string;
  pPrice: string;
  pDescription: string;
  uID: string;
  dateAdded: string;
};

export default function ProductPage() {
  const tableHeader = [
    {
      label: "Product ID",
      key: "pID",
    },
    {
      label: "Product Name",
      key: "pName",
    },
    {
      label: "Product Price",
      key: "pPrice",
    },
    {
      label: "Product Description",
      key: "pDescription",
    },
    {
      label: "User ID",
      key: "uID",
    },
    {
      label: "Date Added",
      key: "dateAdded",
    },
    {
      label: "Action",
      key: "action",
    },
  ]

  // Mock data: 77 products
  const products = Array.from({ length: 77 }, (_, i) => {
    const snackNames = [
      "Choco Crunch Bar", "Salted Potato Chips", "Gummy Bears", "Soda Can (Cola)", "Peanut Butter Cookies", "Fruit Juice Box", "Cheddar Crackers", "Energy Drink", "Vanilla Wafers", "Mixed Nuts",
      "Pretzel Sticks", "Caramel Popcorn", "Granola Bar", "Strawberry Yogurt", "BBQ Corn Chips", "Lemonade Drink", "Chocolate Chip Cookies", "Apple Slices", "Trail Mix", "Rice Crackers",
      "Honey Roasted Peanuts", "Banana Chips", "Cheese Puffs", "Sparkling Water", "Oatmeal Cookies", "Fruit Gummies", "Veggie Chips", "Cola Gummies", "Almond Clusters", "Berry Smoothie",
      "Coconut Water", "Protein Bar", "Sour Candy Mix", "Butter Cookies", "Peach Tea", "Chocolate Wafers", "Popcorn Balls", "Dried Mango", "Yogurt Raisins", "Spicy Snack Mix",
      "Cinnamon Rolls", "Hazelnut Spread", "Grape Juice Box", "Mini Donuts", "Crispy Seaweed", "Mochi Bites", "Peanut Brittle", "Fruit Leather", "Soda Can (Orange)", "Cheddar Popcorn",
      "Chocolate Pretzels", "Apple Chips", "Sweet Potato Fries", "Cranberry Mix", "Coconut Chips", "Lime Soda", "Caramel Wafers", "Berry Yogurt", "Pistachio Nuts", "Energy Bar",
      "Ginger Cookies", "Mango Juice", "Chocolate Almonds", "Raspberry Gummies", "Peanut Butter Cups", "Lemon Cookies", "Coconut Macaroons", "Berry Granola", "Sour Apple Rings", "Cola Bottles",
      "Strawberry Wafers", "Hazelnut Cookies", "Fruit Punch", "Spicy Peanuts", "Chocolate Fudge", "Orange Gummies", "Mint Candy"
    ];
    const users = [
      "Alice", "Bob", "Charlie", "Diana", "Evan", "Fiona", "George", "Hannah", "Ivan", "Julia",
      "Kevin", "Lily", "Mason", "Nina", "Oscar", "Paula", "Quinn", "Rita", "Sam", "Tina"
    ];
    return {
      pID: `PRD${String(i + 1).padStart(3, "0")}`,
      pName: snackNames[i % snackNames.length] + (i >= snackNames.length ? ` ${Math.floor(i / snackNames.length) + 1}` : ""),
      pPrice: `$${(1 + (i % 10) * 0.5 + (i % 3) * 0.25).toFixed(2)}`,
      pDescription: `Tasty ${snackNames[i % snackNames.length].toLowerCase()} for your snack cravings.`,
      uID: users[i % users.length],
      dateAdded: `2024-06-${String((i % 30) + 1).padStart(2, "0")}`,
    }
  })

  // Search state
  const [search, setSearch] = useState("")

  // Pagination state
  const [page, setPage] = useState(1)
  const pageSize = 10

  // Filter products by search
  const filteredProducts = products.filter(product => {
    const searchLower = search.toLowerCase()
    return (
      product.pName.toLowerCase().includes(searchLower) ||
      product.pDescription.toLowerCase().includes(searchLower) ||
      product.uID.toLowerCase().includes(searchLower)
    )
  })

  const pageCount = Math.ceil(filteredProducts.length / pageSize)
  const paginatedProducts = filteredProducts.slice((page - 1) * pageSize, page * pageSize)

  const handlePrev = () => setPage((p) => Math.max(1, p - 1))
  const handleNext = () => setPage((p) => Math.min(pageCount, p + 1))

  // Reset to first page when search changes
  React.useEffect(() => {
    setPage(1)
  }, [search])

  // Dialog state
  const [open, setOpen] = useState(false)
  const [editOpen, setEditOpen] = useState(false)
  const [editProduct, setEditProduct] = useState<Product | null>(null)

  // Handler for opening edit dialog
  const handleEdit = (product: Product) => {
    setEditProduct(product)
    setEditOpen(true)
  }

  return (
    <div className="max-w-6xl mx-auto p-4">
      {/* Add Product Button and Dialog */}
      <div className="mb-4 flex justify-between items-center">
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger asChild>
            <Button className="bg-[#278d9e] hover:bg-[#206b77] text-white font-semibold">Add Product</Button>
          </DialogTrigger>
          <DialogContent className="max-w-md w-full">
            <DialogHeader>
              <DialogTitle>Add Product</DialogTitle>
            </DialogHeader>
            <form className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Product Name</label>
                <input type="text" className="w-full px-3 py-2 border rounded-md bg-white dark:bg-zinc-900 border-zinc-300 dark:border-zinc-700" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Price</label>
                <input
                  type="text"
                  inputMode="decimal"
                  pattern="^\\d*\\.?\\d*$"
                  className="w-full px-3 py-2 border rounded-md bg-white dark:bg-zinc-900 border-zinc-300 dark:border-zinc-700"
                  onInput={e => {
                    const input = e.target as HTMLInputElement;
                    // Only allow numbers and a single dot
                    input.value = input.value
                      .replace(/[^\d.]/g, "") // Remove non-numeric and non-dot
                      .replace(/(\..*)\./g, "$1"); // Only allow one dot
                  }}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Description</label>
                <input type="text" className="w-full px-3 py-2 border rounded-md bg-white dark:bg-zinc-900 border-zinc-300 dark:border-zinc-700" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">User</label>
                <input type="text" className="w-full px-3 py-2 border rounded-md bg-white dark:bg-zinc-900 border-zinc-300 dark:border-zinc-700" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Date Added</label>
                <input type="date" className="w-full px-3 py-2 border rounded-md bg-white dark:bg-zinc-900 border-zinc-300 dark:border-zinc-700" />
              </div>
              <DialogFooter className="mt-4 flex justify-end gap-2">
                <DialogClose asChild>
                  <Button type="button" variant="outline">Cancel</Button>
                </DialogClose>
                <Button type="submit" className="bg-[#278d9e] hover:bg-[#206b77] text-white font-semibold">Add</Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
        {/* Edit Product Dialog */}
        <Dialog open={editOpen} onOpenChange={setEditOpen}>
          <DialogContent className="max-w-md w-full">
            <DialogHeader>
              <DialogTitle>Edit Product</DialogTitle>
            </DialogHeader>
            <form className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Product Name</label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border rounded-md bg-white dark:bg-zinc-900 border-zinc-300 dark:border-zinc-700"
                  value={editProduct?.pName || ""}
                  readOnly
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Price</label>
                <input
                  type="text"
                  inputMode="decimal"
                  pattern="^\\d*\\.?\\d*$"
                  className="w-full px-3 py-2 border rounded-md bg-white dark:bg-zinc-900 border-zinc-300 dark:border-zinc-700"
                  value={editProduct?.pPrice?.replace("$", "") || ""}
                  readOnly
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Description</label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border rounded-md bg-white dark:bg-zinc-900 border-zinc-300 dark:border-zinc-700"
                  value={editProduct?.pDescription || ""}
                  readOnly
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">User</label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border rounded-md bg-white dark:bg-zinc-900 border-zinc-300 dark:border-zinc-700"
                  value={editProduct?.uID || ""}
                  readOnly
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Date Added</label>
                <input
                  type="date"
                  className="w-full px-3 py-2 border rounded-md bg-white dark:bg-zinc-900 border-zinc-300 dark:border-zinc-700"
                  value={editProduct?.dateAdded || ""}
                  readOnly
                />
              </div>
              <DialogFooter className="mt-4 flex justify-end gap-2">
                <DialogClose asChild>
                  <Button type="button" variant="outline">Cancel</Button>
                </DialogClose>
                <Button type="submit" className="bg-[#278d9e] hover:bg-[#206b77] text-white font-semibold">Save</Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
        {/* Search input */}
        <div className="flex-1 flex justify-end ml-4">
          <input
            type="text"
            placeholder="Search by name, description, or user..."
            value={search}
            onChange={e => setSearch(e.target.value)}
            className="w-full max-w-xs px-4 py-2 border border-zinc-300 dark:border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-[#278d9e] bg-white dark:bg-zinc-900 text-zinc-900 dark:text-zinc-100"
          />
        </div>
      </div>
      <div className="bg-white dark:bg-zinc-900 rounded-xl shadow-lg overflow-x-auto border border-zinc-200 dark:border-zinc-800">
        <Table className="min-w-[700px]">
          <TableCaption className="text-base font-medium py-4 text-zinc-500 dark:text-zinc-400">A list of your products.</TableCaption>
          <TableHeader>
            <TableRow className="bg-[#278d9e] dark:bg-zinc-800 text-white hover:bg-black ">
              {tableHeader.map((header) => (
                <TableHead key={header.key} className="px-4 py-3 text-white dark:text-zinc-200 text-sm font-semibold tracking-wide uppercase">
                  {header.label}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedProducts.length > 0 ? (
              paginatedProducts.map((product, idx) => (
                <TableRow key={product.pID} className="hover:bg-zinc-50 dark:hover:bg-zinc-800 transition-colors">
                  <TableCell className="font-medium px-4 py-3">{product.pID}</TableCell>
                  <TableCell className="px-4 py-3">{product.pName}</TableCell>
                  <TableCell className="px-4 py-3">{product.pPrice}</TableCell>
                  <TableCell className="px-4 py-3">{product.pDescription}</TableCell>
                  <TableCell className="px-4 py-3">{product.uID}</TableCell>
                  <TableCell className="px-4 py-3">{product.dateAdded}</TableCell>
                  <TableCell className="px-4 py-3">
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline" onClick={() => handleEdit(product)}>Edit</Button>
                      <Button size="sm" variant="destructive">Delete</Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={tableHeader.length} className="text-center py-8 text-zinc-400">
                  No products found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
        {/* Pagination Controls */}
        <div className="flex items-center justify-between px-4 py-3 bg-zinc-50 dark:bg-zinc-800 border-t border-zinc-200 dark:border-zinc-700">
          <button
            onClick={handlePrev}
            disabled={page === 1}
            className="px-4 py-2 rounded bg-[#278d9e] text-white font-semibold disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[#206b77] transition"
          >
            Previous
          </button>
          <span className="text-zinc-700 dark:text-zinc-200">
            Page {page} of {pageCount || 1}
          </span>
          <button
            onClick={handleNext}
            disabled={page === pageCount || pageCount === 0}
            className="px-4 py-2 rounded bg-[#278d9e] text-white font-semibold disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[#206b77] transition"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  )
}
